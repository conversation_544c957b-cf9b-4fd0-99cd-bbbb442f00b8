import React, { useState } from 'react';
import './PurchasePageSimple.css';

const PurchasePageSimple = () => {
  const [config, setConfig] = useState({
    productType: 'basic',
    deviceCount: 1,
    autoUpgrade: 10,
    duration: 1,
    totalPrice: 34
  });

  // 产品选项
  const productOptions = [
    { value: 'basic', label: '基础版' },
    { value: 'professional', label: '专业版' },
    { value: 'enterprise', label: '企业版' }
  ];

  // 购买时长选项
  const durationOptions = [
    { value: 1, label: '1个月' },
    { value: 3, label: '3个月' },
    { value: 6, label: '6个月' },
    { value: 12, label: '1年', discount: '8折', isPopular: true },
    { value: 24, label: '2年', discount: '7折' },
    { value: 36, label: '3年', discount: '6折' },
    { value: 72, label: '6年', discount: '5折' }
  ];

  // 计算价格
  const calculatePrice = (newConfig) => {
    const basePrice = 34;
    const devicePrice = newConfig.deviceCount * basePrice;
    const upgradePrice = newConfig.autoUpgrade * 2;
    
    let discount = 1;
    if (newConfig.duration >= 72) discount = 0.5;
    else if (newConfig.duration >= 36) discount = 0.6;
    else if (newConfig.duration >= 24) discount = 0.7;
    else if (newConfig.duration >= 12) discount = 0.8;
    
    return Math.round((devicePrice + upgradePrice) * discount);
  };

  // 更新配置
  const updateConfig = (updates) => {
    const newConfig = { ...config, ...updates };
    const totalPrice = calculatePrice(newConfig);
    setConfig({ ...newConfig, totalPrice });
  };

  // 处理购买
  const handlePurchase = () => {
    alert('购买成功！正在跳转到支付页面...');
    console.log('Purchase config:', config);
  };

  return (
    <div className="purchase-page-simple">
      <div className="purchase-container">
        <div className="page-header">
          <h2>☁️ 云安全设备购买套餐购买</h2>
          <p className="page-subtitle">
            了解更多信息或需要帮助请联系我们客服，咨询电话请拨打：400-693-0555
          </p>
        </div>

        <div className="purchase-card">
          <h3>基础版</h3>
          
          {/* 产品选择 */}
          <div className="form-group">
            <label>安全设备套餐</label>
            <select 
              value={config.productType}
              onChange={(e) => updateConfig({ productType: e.target.value })}
            >
              {productOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 设备数量 */}
          <div className="form-group">
            <label>安全设备数量</label>
            <div className="quantity-controls">
              <button 
                onClick={() => updateConfig({ deviceCount: Math.max(1, config.deviceCount - 1) })}
                disabled={config.deviceCount <= 1}
              >
                -
              </button>
              <input 
                type="number" 
                value={config.deviceCount}
                onChange={(e) => updateConfig({ deviceCount: parseInt(e.target.value) || 1 })}
                min="1"
              />
              <button 
                onClick={() => updateConfig({ deviceCount: config.deviceCount + 1 })}
              >
                +
              </button>
              <span>台</span>
            </div>
            <p className="description">
              每个安全设备支持不超过100个用户，（建议安全设备数量不超过（1台）云服务器数量+备用数量）
            </p>
            <div className="recommendation">
              <span className="tag">推荐</span>
              为保证性能建议购买2台以上
            </div>
          </div>

          {/* 自动升级容量 */}
          <div className="form-group">
            <label>自动升级容量</label>
            <div className="quantity-controls">
              <button 
                onClick={() => updateConfig({ autoUpgrade: Math.max(1, config.autoUpgrade - 1) })}
                disabled={config.autoUpgrade <= 1}
              >
                -
              </button>
              <input 
                type="number" 
                value={config.autoUpgrade}
                onChange={(e) => updateConfig({ autoUpgrade: parseInt(e.target.value) || 1 })}
                min="1"
              />
              <button 
                onClick={() => updateConfig({ autoUpgrade: config.autoUpgrade + 1 })}
              >
                +
              </button>
              <span>GB</span>
            </div>
            <p className="description">
              当安全设备容量不足100G时，（建议自动升级容量不超过（2GB）云服务器数量+备用数量）
            </p>
            <div className="recommendation">
              <span className="tag">推荐</span>
              为保证性能建议购买20GB以上容量
            </div>
          </div>

          {/* 购买时长 */}
          <div className="form-group">
            <label>购买时长</label>
            <div className="duration-options">
              {durationOptions.map((option) => (
                <div
                  key={option.value}
                  className={`duration-option ${config.duration === option.value ? 'selected' : ''}`}
                  onClick={() => updateConfig({ duration: option.value })}
                >
                  {option.discount && (
                    <span className="discount-tag">{option.discount}</span>
                  )}
                  {option.isPopular && (
                    <span className="popular-tag">热门</span>
                  )}
                  <div>{option.label}</div>
                </div>
              ))}
            </div>
            <p className="description">
              包年套餐最高可享受30%优惠，我们建议您购买年度套餐。
            </p>
          </div>
        </div>

        {/* 价格显示 */}
        <div className="purchase-footer">
          <div className="price-section">
            <span>配置费用总计：</span>
            <span className="price">{config.totalPrice}</span>
            <span>元/月</span>
            <button className="purchase-btn" onClick={handlePurchase}>
              立即购买
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchasePageSimple;
