.purchase-page-simple {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
  margin-top: 70px;
}

.purchase-container {
  max-width: 800px;
  margin: 0 auto;
}

.page-header h2 {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #1e3a8a;
}

.page-subtitle {
  color: #666;
  margin-bottom: 24px;
  font-size: 14px;
}

.purchase-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.purchase-card h3 {
  color: #1e3a8a;
  margin-bottom: 24px;
  font-size: 18px;
}

.form-group {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.form-group select {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.quantity-controls button {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-controls button:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.quantity-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-controls input {
  width: 60px;
  height: 32px;
  text-align: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.quantity-controls span {
  color: #666;
  margin-left: 4px;
}

.description {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.recommendation {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.tag {
  background: #ff7a00;
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
}

.duration-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.duration-option {
  position: relative;
  width: 80px;
  height: 60px;
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.duration-option:hover {
  border-color: #1890ff;
  transform: translateY(-2px);
}

.duration-option.selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.discount-tag {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
}

.popular-tag {
  position: absolute;
  top: -8px;
  left: -8px;
  background: #ff7a00;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
}

.duration-option div {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.purchase-footer {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  bottom: 20px;
}

.price-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #ff4d4f;
}

.purchase-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-left: 16px;
  transition: background 0.3s ease;
}

.purchase-btn:hover {
  background: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .purchase-page-simple {
    padding: 16px;
  }
  
  .duration-options {
    justify-content: center;
  }
  
  .price-section {
    flex-direction: column;
    gap: 16px;
  }
  
  .purchase-btn {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .duration-options {
    gap: 8px;
  }
  
  .duration-option {
    width: 70px;
    height: 50px;
  }
}
