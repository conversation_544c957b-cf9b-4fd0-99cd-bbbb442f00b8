import React, { useState } from 'react';
import './App.css';
import PurchasePageSimple from './pages/PurchasePageSimple';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const renderHomePage = () => (
    <>
      {/* Hero区域 */}
      <section className="hero" id="home">
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              下一代云安全平台
            </h1>
            <p className="hero-subtitle">
              为企业提供全面的云原生安全解决方案，保护您的数字资产免受网络威胁
            </p>
            <div className="hero-buttons">
              <button className="btn-primary" onClick={() => setCurrentPage('purchase')}>免费试用</button>
              <button className="btn-secondary">了解更多</button>
            </div>
          </div>
          <div className="hero-image">
            <div className="hero-graphic">
              <div className="cloud-icon">☁️</div>
              <div className="shield-icon">🛡️</div>
              <div className="network-lines"></div>
            </div>
          </div>
        </div>
      </section>

      {/* 特性展示 */}
      <section className="features" id="products">
        <div className="container">
          <h2 className="section-title">核心优势</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🔒</div>
              <h3>高级威胁防护</h3>
              <p>基于AI的威胁检测和防护，实时识别和阻止高级持续性威胁</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3>高性能处理</h3>
              <p>毫秒级响应时间，支持大规模并发访问和数据处理</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🌐</div>
              <h3>全球部署</h3>
              <p>多地域部署，就近接入，确保全球用户的最佳访问体验</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">📊</div>
              <h3>智能分析</h3>
              <p>深度学习算法，提供详细的安全态势感知和威胁情报分析</p>
            </div>
          </div>
        </div>
      </section>

      {/* 解决方案 */}
      <section className="solutions" id="solutions">
        <div className="container">
          <h2 className="section-title">解决方案</h2>
          <div className="solutions-grid">
            <div className="solution-item">
              <h3>企业云安全</h3>
              <p>为大型企业提供全方位的云安全防护，包括网络安全、数据保护和合规管理</p>
              <ul>
                <li>✓ 零信任网络架构</li>
                <li>✓ 数据加密与备份</li>
                <li>✓ 合规性管理</li>
              </ul>
            </div>
            <div className="solution-item">
              <h3>中小企业方案</h3>
              <p>针对中小企业的轻量级安全解决方案，简单易用，成本可控</p>
              <ul>
                <li>✓ 一键部署</li>
                <li>✓ 自动化运维</li>
                <li>✓ 灵活计费</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className="stats">
        <div className="container">
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">10,000+</div>
              <div className="stat-label">企业客户</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">99.9%</div>
              <div className="stat-label">服务可用性</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">24/7</div>
              <div className="stat-label">技术支持</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">50+</div>
              <div className="stat-label">全球节点</div>
            </div>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="footer" id="contact">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h4>产品</h4>
              <ul>
                <li><a href="#">云防火墙</a></li>
                <li><a href="#">威胁检测</a></li>
                <li><a href="#">数据保护</a></li>
                <li><a href="#">合规管理</a></li>
              </ul>
            </div>
            <div className="footer-section">
              <h4>解决方案</h4>
              <ul>
                <li><a href="#">企业安全</a></li>
                <li><a href="#">金融行业</a></li>
                <li><a href="#">政府机构</a></li>
                <li><a href="#">教育行业</a></li>
              </ul>
            </div>
            <div className="footer-section">
              <h4>支持</h4>
              <ul>
                <li><a href="#">技术文档</a></li>
                <li><a href="#">在线支持</a></li>
                <li><a href="#">培训服务</a></li>
                <li><a href="#">社区论坛</a></li>
              </ul>
            </div>
            <div className="footer-section">
              <h4>联系我们</h4>
              <p>电话: 400-888-8888</p>
              <p>邮箱: <EMAIL></p>
              <p>地址: 北京市朝阳区xxx大厦</p>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 Hillstone Networks. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </>
  );

  return (
    <div className="App">
      {/* 导航栏 */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="nav-logo">
            <img src="/logo192.png" alt="Hillstone" className="logo-img" />
            <span className="logo-text">Hillstone Cloud</span>
          </div>
          <div className="nav-menu">
            <a href="#home" className="nav-link" onClick={() => setCurrentPage('home')}>首页</a>
            <a href="#products" className="nav-link">产品</a>
            <a href="#solutions" className="nav-link">解决方案</a>
            <a href="#support" className="nav-link">支持</a>
            <a href="#contact" className="nav-link">联系我们</a>
            <button className="nav-btn" onClick={() => setCurrentPage('purchase')}>购买</button>
          </div>
        </div>
      </nav>

      {/* 页面内容 */}
      {currentPage === 'purchase' ? <PurchasePageSimple /> : renderHomePage()}
    </div>
  );
}

export default App;
